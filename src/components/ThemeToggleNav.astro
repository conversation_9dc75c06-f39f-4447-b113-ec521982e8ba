---
---

<button
  id="theme-toggle-nav"
  class="p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
  aria-label="切换主题"
>
  <svg
    id="theme-toggle-dark-icon-nav"
    class="w-5 h-5 hidden"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
  </svg>
  <svg
    id="theme-toggle-light-icon-nav"
    class="w-5 h-5"
    fill="currentColor"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
      clip-rule="evenodd"
    ></path>
  </svg>
</button>

<script is:inline>
  function initNavTheme() {
    const theme = localStorage.getItem('theme') || 'dark';
    const button = document.getElementById('theme-toggle-nav');
    const darkIcon = document.getElementById('theme-toggle-dark-icon-nav');
    const lightIcon = document.getElementById('theme-toggle-light-icon-nav');
    
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      if (button) {
        button.className = 'p-2 rounded-md text-gray-300 hover:text-blue-400 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors';
      }
      if (darkIcon) {
        darkIcon.classList.remove('hidden');
        darkIcon.className = 'w-5 h-5 text-yellow-400';
      }
      if (lightIcon) {
        lightIcon.classList.add('hidden');
      }
    } else {
      document.documentElement.classList.remove('dark');
      if (button) {
        button.className = 'p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors';
      }
      if (lightIcon) {
        lightIcon.classList.remove('hidden');
        lightIcon.className = 'w-5 h-5 text-yellow-500';
      }
      if (darkIcon) {
        darkIcon.classList.add('hidden');
      }
    }
    
    localStorage.setItem('theme', theme);
  }

  function toggleNavTheme() {
    const isDark = document.documentElement.classList.contains('dark');
    const button = document.getElementById('theme-toggle-nav');
    const darkIcon = document.getElementById('theme-toggle-dark-icon-nav');
    const lightIcon = document.getElementById('theme-toggle-light-icon-nav');
    
    if (isDark) {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
      
      if (button) {
        button.className = 'p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors';
      }
      if (darkIcon) darkIcon.classList.add('hidden');
      if (lightIcon) {
        lightIcon.classList.remove('hidden');
        lightIcon.className = 'w-5 h-5 text-yellow-500';
      }
    } else {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
      
      if (button) {
        button.className = 'p-2 rounded-md text-gray-300 hover:text-blue-400 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors';
      }
      if (lightIcon) lightIcon.classList.add('hidden');
      if (darkIcon) {
        darkIcon.classList.remove('hidden');
        darkIcon.className = 'w-5 h-5 text-yellow-400';
      }
    }
  }

  document.addEventListener('DOMContentLoaded', function() {
    initNavTheme();
    
    const themeToggle = document.getElementById('theme-toggle-nav');
    if (themeToggle) {
      themeToggle.addEventListener('click', toggleNavTheme);
    }
  });

  initNavTheme();
</script>