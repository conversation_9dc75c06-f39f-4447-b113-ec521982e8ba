---
---

<button
  id="scroll-to-top"
  class="fixed bottom-8 right-8 z-40 p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 opacity-0 transform translate-y-4 pointer-events-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
  aria-label="回到顶部"
>
  <svg
    class="w-6 h-6"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M5 10l7-7m0 0l7 7m-7-7v18"
    ></path>
  </svg>
</button>

<script is:inline>
  document.addEventListener('DOMContentLoaded', function() {
    const scrollToTopButton = document.getElementById('scroll-to-top');
    
    if (!scrollToTopButton) return;

    // 监听滚动事件
    function handleScroll() {
      if (window.scrollY > 300) {
        // 显示按钮
        scrollToTopButton.classList.remove('opacity-0', 'translate-y-4', 'pointer-events-none');
        scrollToTopButton.classList.add('opacity-100', 'translate-y-0', 'pointer-events-auto');
      } else {
        // 隐藏按钮
        scrollToTopButton.classList.remove('opacity-100', 'translate-y-0', 'pointer-events-auto');
        scrollToTopButton.classList.add('opacity-0', 'translate-y-4', 'pointer-events-none');
      }
    }

    // 点击回到顶部
    function scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // 绑定事件
    window.addEventListener('scroll', handleScroll);
    scrollToTopButton.addEventListener('click', scrollToTop);

    // 初始检查
    handleScroll();
  });
</script>