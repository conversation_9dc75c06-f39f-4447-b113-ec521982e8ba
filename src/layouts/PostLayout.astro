---
import BaseLayout from './BaseLayout.astro';
import ScrollToTop from '../components/ScrollToTop.astro';
import Prose from '../components/Prose.astro';
import TableOfContents from '../components/TableOfContents.astro';

export interface Props {
  title: string;
  description: string;
  image?: string;
  publishDate: Date | string;
  author?: string;
  category?: string;
  tags?: string[];
  readingTime?: string;
  headings?: {
    slug: string;
    text: string;
    depth: number;
  }[]
}

const { 
  title, 
  description, 
  image, 
  publishDate, 
  author = "博主",
  category,
  tags = [],
  readingTime = "5 分钟",
  headings = []
} = Astro.props;

// 确保 publishDate 是 Date 对象
const dateObj = publishDate instanceof Date ? publishDate : new Date(publishDate);

const formattedDate = dateObj.toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
});
---

<BaseLayout 
  title={title}
  description={description}
  image={image}
  type="article"
  class="py-8"
>
  <ScrollToTop />
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
      <!-- Main Content -->
      <div class="lg:col-span-8">
        <article>
          <!-- Article Header -->
          <header class="mb-8">
            <!-- Back Button -->
            <div class="mb-6">
              <a 
                href="/blog" 
                class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回博客列表
              </a>
            </div>

            <!-- Title -->
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight dark:text-white">
              {title}
            </h1>

            <!-- Meta Info -->
            <div class="flex flex-wrap items-center gap-4 text-gray-600 mb-6">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                {author}
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {formattedDate}
              </div>
              {category && (
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7h18M3 12h18M3 17h18" />
                  </svg>
                  <a href={`/categories/${encodeURIComponent(category)}`} class="hover:text-blue-700 text-blue-600">
                    {category}
                  </a>
                </div>
              )}
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                阅读时间 {readingTime}
              </div>
            </div>

            <!-- Tags -->
            {tags.length > 0 && (
              <div class="flex flex-wrap gap-2 mb-8">
                {tags.map((tag) => (
                  <a href={`/tags/${encodeURIComponent(tag)}`} class="inline-block bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full hover:bg-blue-200 transition-colors">
                    #{tag}
                  </a>
                ))}
              </div>
            )}

            <!-- Featured Image -->
            {image && (
              <div class="mb-8">
                <img 
                  src={image} 
                  alt={title}
                  class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            )}
          </header>

          <!-- Article Content -->
          <Prose>
            <slot />
          </Prose>

          <!-- Article Footer -->
          <footer class="mt-12 pt-8 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div class="text-gray-600">
                感谢阅读！如果觉得有用，欢迎分享给更多人。
              </div>
              <div class="flex space-x-4">
                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="分享到微博">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9.331 14.002c-.066 1.067.57 1.985 1.42 2.051.85.066 1.624-.645 1.69-1.712.066-1.067-.57-1.985-1.42-2.051-.85-.066-1.624.645-1.69 1.712zm2.487-.788c-.22.017-.394-.147-.39-.366.005-.219.198-.411.418-.428.22-.017.394.147.39.366-.005.219-.198.411-.418.428z"/>
                  </svg>
                </button>
                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="分享到Twitter">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                  </svg>
                </button>
                <button class="text-gray-400 hover:text-blue-600 transition-colors" title="复制链接">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                  </svg>
                </button>
              </div>
            </div>
          </footer>
        </article>
      </div>

      {
        headings.length > 0 && (
          <!-- Table of Contents Sidebar -->
          <div class="lg:col-span-4">
            <div class="lg:pl-6">
              <TableOfContents headings={headings} class="hidden lg:block" />
              <!-- Mobile TOC -->
              <div class="lg:hidden mb-8">
                <TableOfContents headings={headings} />
              </div>
            </div>
          </div>
        )
      }
    </div>
  </div>
</BaseLayout>
