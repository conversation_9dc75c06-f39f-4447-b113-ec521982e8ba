---
import BaseLayout from './BaseLayout.astro';

export interface Props {
  title?: string;
  description?: string;
  image?: string;
  canonical?: string;
}

const { title, description, image, canonical } = Astro.props;
---

<BaseLayout 
  title={title}
  description={description}
  image={image}
  canonical={canonical}
  class="py-8"
>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {title ?? '博客'}
      </h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {description ?? '技术见解、生活感悟和创意思考的分享空间'}
      </p>
    </div>

    <!-- Main Content -->
    <slot />
  </div>
</BaseLayout>
