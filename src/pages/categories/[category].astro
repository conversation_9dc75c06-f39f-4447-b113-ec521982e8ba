---
import BlogLayout from '../../layouts/BlogLayout.astro';
import { getCollection } from 'astro:content';

export async function getStaticPaths() {
  const posts = await getCollection('blog');
  const set = new Set<string>(posts.map(p => p.data.category));
  return Array.from(set).map(category => ({ params: { category } }));
}

const { category } = Astro.params;
const all = await getCollection('blog');
const filtered = all
  .filter(p => p.data.category === category)
  .map(p => ({ slug: p.slug, ...p.data }))
  .sort((a, b) => b.publishDate.getTime() - a.publishDate.getTime());
---

<BlogLayout
  title={`分类：${category}`}
  description={`属于“${category}”分类的文章`}
  canonical={`/categories/${encodeURIComponent(category!)}`}
>
  {filtered.length === 0 ? (
    <div class="text-center text-gray-500 py-16">暂无相关文章</div>
  ) : (
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      {filtered.map((post) => (
        <article class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all overflow-hidden">
          <div class="aspect-w-16 aspect-h-9">
            <img 
              src={post.image || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=400&fit=crop'} 
              alt={post.title}
              class="w-full h-48 object-cover"
            />
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <a href={`/categories/${encodeURIComponent(post.category)}`} class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full font-medium hover:bg-blue-200 transition-colors">
                {post.category}
              </a>
              {post.featured && (
                <span class="bg-yellow-100 text-yellow-800 text-sm px-3 py-1 rounded-full font-medium ml-2">精选</span>
              )}
              <span class="text-gray-500 text-sm ml-auto flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {post.readingTime}
              </span>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
              <a href={`/blog/${post.slug}`} class="hover:text-blue-600 transition-colors">{post.title}</a>
            </h2>
            <p class="text-gray-600 mb-4 line-clamp-3">{post.description}</p>
            <div class="flex flex-wrap gap-2 mb-4">
              {post.tags.slice(0, 3).map(t => (
                <a href={`/tags/${encodeURIComponent(t)}`} class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded hover:bg-blue-100 hover:text-blue-700 transition-colors">#{t}</a>
              ))}
              {post.tags.length > 3 && (
                <span class="text-xs text-gray-400">+{post.tags.length - 3}</span>
              )}
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                {post.author}
              </div>
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {post.publishDate.toLocaleDateString('zh-CN')}
              </div>
            </div>
          </div>
        </article>
      ))}
    </div>
  )}
</BlogLayout>

