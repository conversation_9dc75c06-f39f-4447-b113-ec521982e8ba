---
import BlogLayout from '../layouts/BlogLayout.astro';
import { getCollection } from 'astro:content';

const posts = await getCollection('blog');
const categoryCounts = new Map<string, number>();
for (const p of posts) {
  const c = p.data.category;
  categoryCounts.set(c, (categoryCounts.get(c) || 0) + 1);
}
const categories = Array.from(categoryCounts.entries())
  .sort((a, b) => a[0].localeCompare(b[0], 'zh-CN'));
---

<BlogLayout
  title="分类"
  description="浏览所有博客分类"
>
  <div class="mb-8 text-center text-gray-600">
    共 {categories.length} 个分类
  </div>

  <div class="flex flex-wrap gap-3">
    {categories.map(([name, count]) => (
      <a href={`/categories/${encodeURIComponent(name)}`} class="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-gray-100 text-gray-700 hover:bg-blue-100 hover:text-blue-700 transition-colors">
        <span>{name}</span>
        <span class="text-xs bg-white/70 text-gray-500 px-2 py-0.5 rounded-full">{count}</span>
      </a>
    ))}
  </div>
</BlogLayout>

